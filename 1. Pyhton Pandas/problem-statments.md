1. Select specific rows and columns
2. filter rows
3. combine multiple conditions

ans1 => df[]
ans2 => boolean conditions

selecting columns return:

1. A series
2. dataFrame of multiple columns

"""
column = df["Column name"]
subset = df["Column1","Column2"]
"""

Filtering rows
boolean indexing

"""
# based on a sngle condition
filtered_row = df[df["ColumnName"] (operator) value]

# based on multiple conditions
filtered_row = df[(df["ColumnName"] (operator) value) & (df["ColumnName"] (operator) value)]
"""

