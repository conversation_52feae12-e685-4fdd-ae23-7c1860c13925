[{"OrderID": 1001, "OrderDate": "2025-09-05", "CustomerID": "C001", "CustomerName": "<PERSON><PERSON>", "Product": "Laptop", "Category": "Electronics", "Quantity": 2, "Price": 55000, "Country": "India", "PaymentMethod": "Credit Card"}, {"OrderID": 1002, "OrderDate": "2025-09-06", "CustomerID": "C002", "CustomerName": "<PERSON>", "Product": "Headphones", "Category": "Electronics", "Quantity": 1, "Price": 3000, "Country": "China", "PaymentMethod": "UPI"}, {"OrderID": 1003, "OrderDate": "2025-09-07", "CustomerID": "C003", "CustomerName": "<PERSON>", "Product": "T-shirt", "Category": "<PERSON><PERSON><PERSON>", "Quantity": 4, "Price": 800, "Country": "Brazil", "PaymentMethod": "Debit Card"}, {"OrderID": 1004, "OrderDate": "2025-09-07", "CustomerID": "C004", "CustomerName": "<PERSON>", "Product": "Notebook", "Category": "Stationery", "Quantity": 3, "Price": 150, "Country": "Russia", "PaymentMethod": "Cash"}, {"OrderID": 1005, "OrderDate": "2025-09-08", "CustomerID": "C005", "CustomerName": "<PERSON>", "Product": "Smartphone", "Category": "Electronics", "Quantity": 1, "Price": 35000, "Country": "Germany", "PaymentMethod": "Credit Card"}, {"OrderID": 1006, "OrderDate": "2025-09-09", "CustomerID": "C001", "CustomerName": "<PERSON><PERSON>", "Product": "Backpack", "Category": "<PERSON><PERSON><PERSON>", "Quantity": 2, "Price": 1200, "Country": "India", "PaymentMethod": "PayPal"}, {"OrderID": 1007, "OrderDate": "2025-09-10", "CustomerID": "C006", "CustomerName": "<PERSON><PERSON><PERSON>", "Product": "Pen", "Category": "Stationery", "Quantity": 10, "Price": 20, "Country": "Japan", "PaymentMethod": "Cash"}, {"OrderID": 1008, "OrderDate": "2025-09-11", "CustomerID": "C007", "CustomerName": "<PERSON><PERSON>", "Product": "Bluetooth Speaker", "Category": "Electronics", "Quantity": 1, "Price": 2500, "Country": "UAE", "PaymentMethod": "Debit Card"}, {"OrderID": 1009, "OrderDate": "2025-09-12", "CustomerID": "C008", "CustomerName": "<PERSON>", "Product": "Mouse", "Category": "Electronics", "Quantity": 2, "Price": 700, "Country": "UK", "PaymentMethod": "Apple Pay"}, {"OrderID": 1010, "OrderDate": "2025-09-13", "CustomerID": "C009", "CustomerName": "<PERSON>", "Product": "<PERSON>g", "Category": "Home", "Quantity": 3, "Price": 250, "Country": "USA", "PaymentMethod": "Credit Card"}, {"OrderID": 1011, "OrderDate": "2025-09-14", "CustomerID": "C010", "CustomerName": "<PERSON>", "Product": "Bag", "Category": "<PERSON><PERSON><PERSON>", "Quantity": 1, "Price": 950, "Country": "Spain", "PaymentMethod": "UPI"}, {"OrderID": 1012, "OrderDate": "2025-09-15", "CustomerID": "C011", "CustomerName": "<PERSON>", "Product": "Chair", "Category": "Home", "Quantity": 1, "Price": 3500, "Country": "France", "PaymentMethod": "Debit Card"}, {"OrderID": 1013, "OrderDate": "2025-09-16", "CustomerID": "C012", "CustomerName": "<PERSON>", "Product": "Water Bottle", "Category": "Home", "Quantity": 5, "Price": 120, "Country": "India", "PaymentMethod": "PayPal"}, {"OrderID": 1014, "OrderDate": "2025-09-17", "CustomerID": "C013", "CustomerName": "<PERSON>", "Product": "Keyboard", "Category": "Electronics", "Quantity": 1, "Price": 1800, "Country": "China", "PaymentMethod": "Cash"}, {"OrderID": 1015, "OrderDate": "2025-09-18", "CustomerID": "C014", "CustomerName": "<PERSON>", "Product": "<PERSON><PERSON>", "Category": "Home", "Quantity": 2, "Price": 900, "Country": "Italy", "PaymentMethod": "Credit Card"}, {"OrderID": 1016, "OrderDate": "2025-09-19", "CustomerID": "C015", "CustomerName": "<PERSON>", "Product": "Coffee Table", "Category": "Home", "Quantity": 1, "Price": 4200, "Country": "Poland", "PaymentMethod": "Debit Card"}, {"OrderID": 1017, "OrderDate": "2025-09-20", "CustomerID": "C016", "CustomerName": "<PERSON>", "Product": "Notebook", "Category": "Stationery", "Quantity": 5, "Price": 150, "Country": "South Korea", "PaymentMethod": "UPI"}, {"OrderID": 1018, "OrderDate": "2025-09-21", "CustomerID": "C017", "CustomerName": "<PERSON>", "Product": "Headphones", "Category": "Electronics", "Quantity": 2, "Price": 3000, "Country": "Egypt", "PaymentMethod": "PayPal"}, {"OrderID": 1019, "OrderDate": "2025-09-22", "CustomerID": "C018", "CustomerName": "<PERSON><PERSON>", "Product": "T-shirt", "Category": "<PERSON><PERSON><PERSON>", "Quantity": 3, "Price": 800, "Country": "Brazil", "PaymentMethod": "Debit Card"}, {"OrderID": 1020, "OrderDate": "2025-09-23", "CustomerID": "C019", "CustomerName": "<PERSON>", "Product": "Mouse", "Category": "Electronics", "Quantity": 1, "Price": 700, "Country": "Canada", "PaymentMethod": "Credit Card"}, {"OrderID": 1021, "OrderDate": "2025-09-24", "CustomerID": "C020", "CustomerName": "<PERSON>", "Product": "Smartphone", "Category": "Electronics", "Quantity": 1, "Price": 34000, "Country": "Spain", "PaymentMethod": "UPI"}, {"OrderID": 1022, "OrderDate": "2025-09-25", "CustomerID": "C021", "CustomerName": "<PERSON>", "Product": "Bag", "Category": "<PERSON><PERSON><PERSON>", "Quantity": 2, "Price": 950, "Country": "Saudi Arabia", "PaymentMethod": "Apple Pay"}, {"OrderID": 1023, "OrderDate": "2025-09-26", "CustomerID": "C022", "CustomerName": "<PERSON><PERSON>", "Product": "Chair", "Category": "Home", "Quantity": 2, "Price": 3500, "Country": "Japan", "PaymentMethod": "Credit Card"}, {"OrderID": 1024, "OrderDate": "2025-09-27", "CustomerID": "C023", "CustomerName": "<PERSON><PERSON>", "Product": "Pen", "Category": "Stationery", "Quantity": 15, "Price": 20, "Country": "India", "PaymentMethod": "Cash"}, {"OrderID": 1025, "OrderDate": "2025-09-28", "CustomerID": "C024", "CustomerName": "<PERSON>", "Product": "<PERSON>g", "Category": "Home", "Quantity": 2, "Price": 250, "Country": "Norway", "PaymentMethod": "Debit Card"}, {"OrderID": 1026, "OrderDate": "2025-09-29", "CustomerID": "C003", "CustomerName": "<PERSON>", "Product": "Backpack", "Category": "<PERSON><PERSON><PERSON>", "Quantity": 1, "Price": 1200, "Country": "Brazil", "PaymentMethod": "UPI"}, {"OrderID": 1027, "OrderDate": "2025-09-30", "CustomerID": "C013", "CustomerName": "<PERSON>", "Product": "Keyboard", "Category": "Electronics", "Quantity": 1, "Price": 1800, "Country": "China", "PaymentMethod": "PayPal"}, {"OrderID": 1028, "OrderDate": "2025-09-30", "CustomerID": "C007", "CustomerName": "<PERSON><PERSON>", "Product": "Bluetooth Speaker", "Category": "Electronics", "Quantity": 1, "Price": 2500, "Country": "UAE", "PaymentMethod": "Debit Card"}, {"OrderID": 1029, "OrderDate": "2025-10-01", "CustomerID": "C025", "CustomerName": "<PERSON>", "Product": "<PERSON><PERSON>", "Category": "Home", "Quantity": 1, "Price": 900, "Country": "UK", "PaymentMethod": "Cash"}, {"OrderID": 1030, "OrderDate": "2025-10-01", "CustomerID": "C002", "CustomerName": "<PERSON>", "Product": "Mouse", "Category": "Electronics", "Quantity": 2, "Price": 700, "Country": "China", "PaymentMethod": "Apple Pay"}, {"OrderID": 1031, "OrderDate": "2025-10-02", "CustomerID": "C026", "CustomerName": "<PERSON>", "Product": "T-shirt", "Category": "<PERSON><PERSON><PERSON>", "Quantity": 2, "Price": 800, "Country": "Germany", "PaymentMethod": "Credit Card"}, {"OrderID": 1032, "OrderDate": "2025-10-02", "CustomerID": "C006", "CustomerName": "<PERSON><PERSON><PERSON>", "Product": "Bag", "Category": "<PERSON><PERSON><PERSON>", "Quantity": 1, "Price": 950, "Country": "Japan", "PaymentMethod": "Debit Card"}, {"OrderID": 1033, "OrderDate": "2025-10-03", "CustomerID": "C027", "CustomerName": "<PERSON><PERSON><PERSON>", "Product": "Smartphone", "Category": "Electronics", "Quantity": 1, "Price": 35000, "Country": "India", "PaymentMethod": "UPI"}, {"OrderID": 1034, "OrderDate": "2025-10-03", "CustomerID": "C012", "CustomerName": "<PERSON>", "Product": "Pen", "Category": "Stationery", "Quantity": 10, "Price": 20, "Country": "India", "PaymentMethod": "PayPal"}, {"OrderID": 1035, "OrderDate": "2025-10-03", "CustomerID": "C005", "CustomerName": "<PERSON>", "Product": "Laptop", "Category": "Electronics", "Quantity": 1, "Price": 55000, "Country": "Germany", "PaymentMethod": "Credit Card"}, {"OrderID": 1036, "OrderDate": "2025-10-04", "CustomerID": "C021", "CustomerName": "<PERSON>", "Product": "Chair", "Category": "Home", "Quantity": 1, "Price": 3500, "Country": "Saudi Arabia", "PaymentMethod": "Cash"}, {"OrderID": 1037, "OrderDate": "2025-10-04", "CustomerID": "C018", "CustomerName": "<PERSON><PERSON>", "Product": "Headphones", "Category": "Electronics", "Quantity": 1, "Price": 3000, "Country": "Brazil", "PaymentMethod": "Debit Card"}, {"OrderID": 1038, "OrderDate": "2025-10-04", "CustomerID": "C008", "CustomerName": "<PERSON>", "Product": "Water Bottle", "Category": "Home", "Quantity": 1, "Price": 120, "Country": "UK", "PaymentMethod": "Credit Card"}, {"OrderID": 1039, "OrderDate": "2025-10-04", "CustomerID": "C010", "CustomerName": "<PERSON>", "Product": "Notebook", "Category": "Stationery", "Quantity": 2, "Price": 150, "Country": "Spain", "PaymentMethod": "Debit Card"}, {"OrderID": 1040, "OrderDate": "2025-10-04", "CustomerID": "C015", "CustomerName": "<PERSON>", "Product": "<PERSON><PERSON>", "Category": "Home", "Quantity": 2, "Price": 900, "Country": "Poland", "PaymentMethod": "Apple Pay"}]