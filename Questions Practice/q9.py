class Solution(object):
    def twoSum(self, nums, target):
        """
        :type nums: List[int]
        :type target: int
        :rtype: List[int]
        """
        for i in range(len(nums)):
            for j in range(i + 1, len(nums)):  # Start from i+1 to avoid using same element twice
                if nums[i] + nums[j] == target:
                    return [i, j]

num_func = Solution()
result = num_func.twoSum([3, 2, 4], 6)  # Store the result
print(result)  # Print the actual result
