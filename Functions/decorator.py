"""
burger- function
extra cheese - extra feature

main function > function add
without changing the main function code.
"""

def myDecorator(func):
  def wrapper():
    print("Something is happening before the function.")
    func()
    print("Something is happening after the function")
  return wrapper

@myDecorator #@ se jo andar ka param hai wo bas active hoga
def say_hello():
  print("Hello")

say_hello()
