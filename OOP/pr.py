class Character:
  def __init__(self,name,health,attack,blood):
    self.name = name
    self.health = health
    self.attack = attack
    self.blood = blood

  def attack_enemy(self):
    print(f'Name: {self.name}, Health: {self.health}, Attack: {self.attack}, Blood: {self.blood}')

warrior = Character('Thor',100,50,'Red')

warrior.attack_enemy()

"""
Groups data into a single unit.
"""

"""
1. Classes and objects
2. Inheritance
3. Encapsulation
4. Abstraction
5. Polymorphism
"""
