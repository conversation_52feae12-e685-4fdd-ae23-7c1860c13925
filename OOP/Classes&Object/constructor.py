"""

Object ki properties ko initialize krne ka kaam krta hai

__init__()

Syntax:

class ClassName:
  def __init__(self,param1,param2):
    self.param1 = param1
    self.param2 = param2

Three types:
1. Default
2. Parameterized
3. Constructor with default values


"""

class Car:
  def __init__(self,brand,color):
    self.brand = brand
    self.color = color


car1 = Car('Tesla','Blue')
print(car1.brand, car1.color)
