class BankAccount:
  def __init__(self,accountNumber, balance):
    self.accountNumber = accountNumber
    self.__balance = balance #Private variable ( __name)

  def deposit(self,amount):
    self.__balance += amount
    print(f'Deposited {amount}, New Balance {self.__balance}')

  def get_balance(self):
    return self.__balance


account = BankAccount('1234',5000)

account.deposit(2000)

print(account.get_balance())


"""
Encapsulation makes a elemnt private 
"""
