# Definition for singly-linked list.
# class ListNode(object):
#     def __init__(self, val=0, next=None):
#         self.val = val
#         self.next = next
class Solution(object):
    def addTwoNumbers(self, l1, l2):
        """
        :type l1: Optional[ListNode]
        :type l2: Optional[ListNode]
        :rtype: Optional[ListNode]
        """
        reversed_l1 = l1.reverse()
        reversed_l2 = l2.reverse()
        for i in range(len(reversed_l1)):
          for j in range(len(reversed_l2)):
            l3 =[]
            l3.append(reversed_l1[i] + reversed_l2[j])

            return l3.reverse()

output = Solution()
output.addTwoNumbers([2,4,3],[5,6,4])

print(output)
